# ESP32-C3 IMU Fusion项目

基于ESP32-C3和Fusion库的IMU传感器融合项目，实现高精度的姿态估计。

## 项目特性

- 🎯 **高精度姿态估计**: 使用先进的AHRS算法融合陀螺仪、加速度计数据
- 🚀 **优化性能**: 专为ESP32-C3优化，支持100Hz实时数据处理
- 🔧 **易于扩展**: 模块化设计，支持多种IMU传感器
- 📊 **实时监控**: 通过串口输出姿态角和线性加速度数据

## 硬件要求

### ESP32-C3开发板
- ESP32-C3-DevKitM-1 或兼容开发板
- USB-C数据线

### IMU传感器 (推荐MPU6050)
- MPU6050 6轴IMU传感器模块
- 或其他兼容的I2C接口IMU传感器

## 硬件连接

### MPU6050连接方式
```
ESP32-C3    MPU6050
--------    -------
3.3V    ->  VCC
GND     ->  GND
GPIO8   ->  SCL
GPIO10  ->  SDA
```

## 软件环境

### 必需工具
- ESP-IDF v5.0 或更高版本
- VSCode + ESP-IDF插件
- Python 3.8+

### 安装步骤

1. **安装ESP-IDF**
   ```bash
   # 按照官方文档安装ESP-IDF
   # https://docs.espressif.com/projects/esp-idf/zh_CN/latest/esp32c3/get-started/
   ```

2. **克隆项目**
   ```bash
   git clone <项目地址>
   cd esp32_fusion_imu
   ```

3. **配置项目**
   ```bash
   idf.py menuconfig
   ```

4. **编译项目**
   ```bash
   idf.py build
   ```

5. **烧录到ESP32-C3**
   ```bash
   idf.py flash monitor
   ```

## 使用方法

### 基本使用
1. 连接硬件按照上述连接图
2. 烧录程序到ESP32-C3
3. 打开串口监视器查看输出数据

### 输出数据格式
```
I (1234) IMU_FUSION: 姿态角: Roll=1.2°, Pitch=-0.8°, Yaw=45.6°
I (1244) IMU_FUSION: 线性加速度: X=0.02g, Y=-0.01g, Z=0.00g
```

## 项目结构

```
esp32_fusion_imu/
├── main/                   # 主程序
│   ├── main.c             # 主程序入口
│   ├── imu_driver.c       # IMU驱动实现
│   ├── imu_driver.h       # IMU驱动头文件
│   └── CMakeLists.txt     # 主程序构建配置
├── components/            # 组件目录
│   └── fusion/           # Fusion库组件
│       ├── include/      # 头文件
│       ├── src/          # 源文件
│       └── CMakeLists.txt
├── CMakeLists.txt        # 项目构建配置
├── sdkconfig.defaults    # 默认配置
└── README.md            # 项目说明
```

## 配置选项

### AHRS算法参数
在 `main/main.c` 中可以调整以下参数：

- `gain`: 融合增益 (0.1-1.0，推荐0.5)
- `gyroscopeRange`: 陀螺仪量程 (度/秒)
- `accelerationRejection`: 加速度拒绝阈值 (度)
- `magneticRejection`: 磁场拒绝阈值 (度)

### 采样频率
- 默认100Hz (10ms间隔)
- 可在 `SAMPLE_PERIOD_MS` 中修改

## 故障排除

### 常见问题

1. **编译错误**
   - 确保ESP-IDF版本 >= 5.0
   - 检查环境变量设置

2. **IMU检测失败**
   - 检查I2C连接
   - 确认传感器地址 (默认0x68)
   - 检查电源供应

3. **数据异常**
   - 校准传感器
   - 调整算法参数
   - 检查采样频率

## 扩展功能

### 添加磁力计支持
1. 修改 `imu_driver.c` 添加磁力计读取
2. 在 `main.c` 中启用磁力计数据融合

### 数据记录
1. 添加SD卡支持
2. 实现数据记录功能

### 无线传输
1. 启用WiFi功能
2. 实现UDP/TCP数据传输

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request！

## 参考资料

- [Fusion库官方文档](https://github.com/xioTechnologies/Fusion)
- [ESP32-C3技术参考手册](https://www.espressif.com/sites/default/files/documentation/esp32-c3_technical_reference_manual_cn.pdf)
- [MPU6050数据手册](https://invensense.tdk.com/wp-content/uploads/2015/02/MPU-6000-Datasheet1.pdf)
