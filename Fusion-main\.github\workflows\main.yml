name: CI

on:
  push:
    branches: [ main ]
    tags: [ "*" ]
  pull_request:
    branches: [ main ]

jobs:
  build_wheels:
    runs-on: ${{ matrix.target[0] }}

    strategy:
      fail-fast: false
      matrix:
        target: # [ os, cibuildwheel-platform ]
          - [ macos-latest, macosx_x86_64 ]
          - [ macos-latest, macosx_arm64 ]
          - [ ubuntu-latest, manylinux_aarch64 ]
          - [ ubuntu-latest, manylinux_x86_64 ]
          - [ windows-latest, win_amd64 ]

        python-version: [ "cp38-", "cp39-", "cp310-", "cp311-", "cp312-", "cp313-" ]

    steps:
      - uses: actions/checkout@v4

      - name: Build CMake targets
        run: |
          mkdir cmake-build-release
          cd cmake-build-release
          cmake .. -DCMAKE_BUILD_TYPE=Release
          cmake --build .

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip

      - if: matrix.target[0] == 'ubuntu-latest'
        name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: all

      - name: Build wheel
        uses: pypa/cibuildwheel@v2.22.0
        env:
          CIBW_ARCHS: all
          CIBW_BUILD: ${{ matrix.python-version }}${{ matrix.target[1] }}
          CIBW_BEFORE_TEST: pip install matplotlib
          CIBW_TEST_COMMAND: cd {project}/Python && python advanced_example.py no_block && python simple_example.py no_block
          CIBW_TEST_COMMAND_WINDOWS: cd /D {project}/Python && python advanced_example.py no_block && python simple_example.py no_block
          CIBW_TEST_SKIP: "*-macosx_arm64" # fix CI warning

      - uses: actions/upload-artifact@v4
        with:
          name: wheel-${{ matrix.python-version }}${{ matrix.target[1] }}
          path: wheelhouse/*

  build_sdist:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install numpy

      - name: Build sdist
        run: python setup.py sdist

      - uses: actions/upload-artifact@v4
        with:
          name: sdist
          path: dist/*

  publish:
    runs-on: ubuntu-latest
    needs: [ build_wheels, build_sdist ]
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
      - uses: actions/download-artifact@v4
        with:
          path: artifacts
          merge-multiple: true

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install twine==6.0.1
        # use twine v6.0.1 because bug in latest version (v6.1.0)    

      - name: Publish package
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYTHON_TOKEN }}
        run: python -m twine upload --verbose --repository pypi artifacts/* --skip-existing
