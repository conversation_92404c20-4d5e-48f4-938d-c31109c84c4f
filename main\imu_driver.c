/**
 * @file imu_driver.c
 * @brief IMU传感器驱动实现 (MPU6050示例)
 * <AUTHOR> IMU Fusion Project
 */

#include <stdio.h>
#include <string.h>
#include "esp_log.h"
#include "driver/i2c.h"
#include "imu_driver.h"

static const char *TAG = "IMU_DRIVER";

// 量程转换因子
static float gyro_scale = 131.0f;    // ±250°/s时的LSB/(°/s)
static float accel_scale = 16384.0f; // ±2g时的LSB/g

/**
 * @brief I2C写寄存器
 */
static esp_err_t i2c_write_reg(uint8_t dev_addr, uint8_t reg_addr, uint8_t data)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (dev_addr << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg_addr, true);
    i2c_master_write_byte(cmd, data, true);
    i2c_master_stop(cmd);
    esp_err_t ret = i2c_master_cmd_begin(I2C_MASTER_NUM, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return ret;
}

/**
 * @brief I2C读寄存器
 */
static esp_err_t i2c_read_reg(uint8_t dev_addr, uint8_t reg_addr, uint8_t *data, size_t len)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (dev_addr << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg_addr, true);
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (dev_addr << 1) | I2C_MASTER_READ, true);
    if (len > 1) {
        i2c_master_read(cmd, data, len - 1, I2C_MASTER_ACK);
    }
    i2c_master_read_byte(cmd, data + len - 1, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    esp_err_t ret = i2c_master_cmd_begin(I2C_MASTER_NUM, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return ret;
}

/**
 * @brief 初始化I2C总线
 */
static esp_err_t i2c_master_init(void)
{
    i2c_config_t conf = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = I2C_MASTER_SDA_IO,
        .scl_io_num = I2C_MASTER_SCL_IO,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = I2C_MASTER_FREQ_HZ,
    };

    esp_err_t err = i2c_param_config(I2C_MASTER_NUM, &conf);
    if (err != ESP_OK) {
        return err;
    }

    return i2c_driver_install(I2C_MASTER_NUM, conf.mode, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
}

/**
 * @brief 初始化MPU6050
 */
static esp_err_t mpu6050_init(void)
{
    esp_err_t ret;
    uint8_t who_am_i;

    // 检查设备ID
    ret = i2c_read_reg(MPU6050_ADDR, MPU6050_WHO_AM_I, &who_am_i, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "无法读取MPU6050 WHO_AM_I寄存器");
        return ret;
    }

    if (who_am_i != 0x68) {
        ESP_LOGE(TAG, "MPU6050设备ID错误: 0x%02X (期望: 0x68)", who_am_i);
        return ESP_ERR_NOT_FOUND;
    }

    ESP_LOGI(TAG, "检测到MPU6050设备 (ID: 0x%02X)", who_am_i);

    // 复位设备
    ret = i2c_write_reg(MPU6050_ADDR, MPU6050_PWR_MGMT_1, 0x80);
    if (ret != ESP_OK) return ret;
    vTaskDelay(pdMS_TO_TICKS(100)); // 等待复位完成

    // 唤醒设备，选择PLL时钟源
    ret = i2c_write_reg(MPU6050_ADDR, MPU6050_PWR_MGMT_1, 0x01);
    if (ret != ESP_OK) return ret;
    vTaskDelay(pdMS_TO_TICKS(100));

    // 配置陀螺仪量程 (±250°/s)
    ret = i2c_write_reg(MPU6050_ADDR, MPU6050_GYRO_CONFIG, MPU6050_GYRO_FS_250);
    if (ret != ESP_OK) return ret;
    gyro_scale = 131.0f; // LSB/(°/s) for ±250°/s

    // 配置加速度计量程 (±2g)
    ret = i2c_write_reg(MPU6050_ADDR, MPU6050_ACCEL_CONFIG, MPU6050_ACCEL_FS_2G);
    if (ret != ESP_OK) return ret;
    accel_scale = 16384.0f; // LSB/g for ±2g

    ESP_LOGI(TAG, "MPU6050初始化完成");
    return ESP_OK;
}

/**
 * @brief 初始化IMU传感器
 */
esp_err_t imu_init(void)
{
    esp_err_t ret;

    // 初始化I2C总线
    ret = i2c_master_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "I2C总线初始化完成");

    // 初始化MPU6050
    ret = mpu6050_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "MPU6050初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    return ESP_OK;
}

/**
 * @brief 读取IMU数据
 */
esp_err_t imu_read_data(float *accel_x, float *accel_y, float *accel_z,
                       float *gyro_x, float *gyro_y, float *gyro_z)
{
    uint8_t data[14];
    esp_err_t ret;

    // 从加速度计寄存器开始连续读取14字节数据
    // 包括: ACCEL_X, ACCEL_Y, ACCEL_Z, TEMP, GYRO_X, GYRO_Y, GYRO_Z
    ret = i2c_read_reg(MPU6050_ADDR, MPU6050_ACCEL_XOUT_H, data, 14);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "读取IMU数据失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 解析加速度计数据 (16位有符号整数，高字节在前)
    int16_t accel_raw_x = (int16_t)((data[0] << 8) | data[1]);
    int16_t accel_raw_y = (int16_t)((data[2] << 8) | data[3]);
    int16_t accel_raw_z = (int16_t)((data[4] << 8) | data[5]);

    // 解析陀螺仪数据 (跳过温度数据data[6-7])
    int16_t gyro_raw_x = (int16_t)((data[8] << 8) | data[9]);
    int16_t gyro_raw_y = (int16_t)((data[10] << 8) | data[11]);
    int16_t gyro_raw_z = (int16_t)((data[12] << 8) | data[13]);

    // 转换为物理单位
    *accel_x = accel_raw_x / accel_scale;  // g
    *accel_y = accel_raw_y / accel_scale;  // g
    *accel_z = accel_raw_z / accel_scale;  // g

    *gyro_x = gyro_raw_x / gyro_scale;     // °/s
    *gyro_y = gyro_raw_y / gyro_scale;     // °/s
    *gyro_z = gyro_raw_z / gyro_scale;     // °/s

    return ESP_OK;
}
