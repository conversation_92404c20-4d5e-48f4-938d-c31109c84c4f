@echo off
echo ========================================
echo ESP32-C3 IMU项目设置和构建脚本
echo ========================================

REM 检查ESP-IDF环境
if not defined IDF_PATH (
    echo 错误: ESP-IDF环境未设置
    echo 请先运行ESP-IDF的export.bat脚本
    echo 例如: C:\esp\esp-idf\export.bat
    echo.
    echo 或者设置ESP-IDF环境变量:
    echo set IDF_PATH=C:\esp\esp-idf
    echo set PATH=%IDF_PATH%\tools;%PATH%
    pause
    exit /b 1
)

echo ESP-IDF路径: %IDF_PATH%
echo.

REM 清理之前的构建
echo 清理之前的构建文件...
if exist build rmdir /s /q build
if exist sdkconfig del sdkconfig

REM 设置目标芯片
echo 设置目标芯片为ESP32-C3...
idf.py set-target esp32c3
if %ERRORLEVEL% NEQ 0 (
    echo 设置目标芯片失败
    pause
    exit /b 1
)

REM 编译项目
echo.
echo 开始编译项目...
idf.py build
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo 编译成功！
    echo.
    echo 现在可以使用以下命令烧录:
    echo idf.py flash monitor
    echo.
    echo 或者运行:
    echo idf.py -p COM3 flash monitor
    echo (请将COM3替换为您的实际串口)
    echo ========================================
) else (
    echo ========================================
    echo 编译失败，请检查错误信息
    echo ========================================
)

pause
