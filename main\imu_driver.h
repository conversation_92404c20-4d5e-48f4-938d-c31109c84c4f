/**
 * @file imu_driver.h
 * @brief IMU传感器驱动头文件
 * <AUTHOR> IMU Fusion Project
 */

#ifndef IMU_DRIVER_H
#define IMU_DRIVER_H

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

// I2C配置
#define I2C_MASTER_SCL_IO           5    /*!< GPIO number used for I2C master clock */
#define I2C_MASTER_SDA_IO           4    /*!< GPIO number used for I2C master data  */
#define I2C_MASTER_NUM              0    /*!< I2C master i2c port number, the number of i2c peripheral interfaces available will depend on the chip */
#define I2C_MASTER_FREQ_HZ          400000     /*!< I2C master clock frequency */
#define I2C_MASTER_TX_BUF_DISABLE   0    /*!< I2C master doesn't need buffer */
#define I2C_MASTER_RX_BUF_DISABLE   0    /*!< I2C master doesn't need buffer */
#define I2C_MASTER_TIMEOUT_MS       1000

// MPU6050 I2C地址和寄存器定义
#define MPU6050_ADDR                0x68
#define MPU6050_WHO_AM_I            0x75
#define MPU6050_PWR_MGMT_1          0x6B
#define MPU6050_GYRO_CONFIG         0x1B
#define MPU6050_ACCEL_CONFIG        0x1C
#define MPU6050_ACCEL_XOUT_H        0x3B
#define MPU6050_GYRO_XOUT_H         0x43

// 量程配置
#define MPU6050_GYRO_FS_250         0x00  // ±250°/s
#define MPU6050_GYRO_FS_500         0x08  // ±500°/s
#define MPU6050_GYRO_FS_1000        0x10  // ±1000°/s
#define MPU6050_GYRO_FS_2000        0x18  // ±2000°/s

#define MPU6050_ACCEL_FS_2G         0x00  // ±2g
#define MPU6050_ACCEL_FS_4G         0x08  // ±4g
#define MPU6050_ACCEL_FS_8G         0x10  // ±8g
#define MPU6050_ACCEL_FS_16G        0x18  // ±16g

/**
 * @brief 初始化IMU传感器
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t imu_init(void);

/**
 * @brief 读取IMU数据
 * @param accel_x 加速度X轴输出 (g)
 * @param accel_y 加速度Y轴输出 (g)
 * @param accel_z 加速度Z轴输出 (g)
 * @param gyro_x 陀螺仪X轴输出 (度/秒)
 * @param gyro_y 陀螺仪Y轴输出 (度/秒)
 * @param gyro_z 陀螺仪Z轴输出 (度/秒)
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t imu_read_data(float *accel_x, float *accel_y, float *accel_z,
                       float *gyro_x, float *gyro_y, float *gyro_z);

#ifdef __cplusplus
}
#endif

#endif // IMU_DRIVER_H
