# ESP32-C3 IMU Fusion项目默认配置

# 目标芯片配置
CONFIG_IDF_TARGET="esp32c3"

# 编译器优化
CONFIG_COMPILER_OPTIMIZATION_SIZE=y

# 日志配置
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE=y

# FreeRTOS配置
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER=y

# I2C配置
CONFIG_I2C_ENABLE_DEBUG_LOG=y

# 内存配置
CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192

# 看门狗配置
CONFIG_ESP_TASK_WDT_TIMEOUT_S=10

# 串口配置
CONFIG_ESP_CONSOLE_UART_DEFAULT=y
CONFIG_ESP_CONSOLE_UART_BAUDRATE_115200=y

# WiFi和蓝牙禁用（节省内存）
CONFIG_ESP_WIFI_ENABLED=n
CONFIG_BT_ENABLED=n

# 电源管理
CONFIG_PM_ENABLE=y
