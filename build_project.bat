@echo off
echo ========================================
echo ESP32-C3 IMU Fusion 项目构建脚本
echo ========================================

REM 检查ESP-IDF环境
if not defined IDF_PATH (
    echo 错误: ESP-IDF环境未设置
    echo 请先运行ESP-IDF的export.bat脚本
    echo 例如: C:\esp\esp-idf\export.bat
    pause
    exit /b 1
)

echo ESP-IDF路径: %IDF_PATH%

REM 设置目标芯片
echo 设置目标芯片为ESP32-C3...
idf.py set-target esp32c3

REM 编译项目
echo 开始编译项目...
idf.py build

if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo 编译成功！
    echo 现在可以使用以下命令烧录:
    echo idf.py flash monitor
    echo ========================================
) else (
    echo ========================================
    echo 编译失败，请检查错误信息
    echo ========================================
)

pause
