# 🚀 ESP32-C3 IMU Fusion 快速入门指南

## 📋 准备工作

### 1. 硬件准备
- ✅ ESP32-C3开发板 (如ESP32-C3-DevKitM-1)
- ✅ MPU6050 IMU传感器模块
- ✅ 杜邦线若干
- ✅ USB-C数据线

### 2. 软件环境
- ✅ VSCode + ESP-IDF插件
- ✅ ESP-IDF v5.0+

### 3. ESP-IDF安装和配置

如果您还没有安装ESP-IDF，请按照以下步骤：

#### Windows用户：
1. 下载ESP-IDF安装器：https://dl.espressif.com/dl/esp-idf/
2. 运行安装器，选择ESP-IDF v5.0或更高版本
3. 安装完成后，桌面会有"ESP-IDF Command Prompt"快捷方式

#### 手动设置环境变量：
如果需要手动设置，请在PowerShell中运行：
```powershell
# 设置ESP-IDF路径 (请根据实际安装路径修改)
$env:IDF_PATH = "C:\esp\esp-idf"
$env:PATH = "$env:IDF_PATH\tools;$env:PATH"

# 或者运行ESP-IDF的export脚本
C:\esp\esp-idf\export.bat
```

## 🔌 硬件连接

```
ESP32-C3 开发板    MPU6050 模块
--------------    ------------
3.3V          ->  VCC
GND           ->  GND
GPIO5 (SCL)   ->  SCL
GPIO4 (SDA)   ->  SDA
```

**注意**: GPIO4和GPIO5是ESP32-C3推荐的I2C引脚，具有内置上拉电阻。

## 💻 VSCode操作步骤

### 1. 打开项目
1. 启动VSCode
2. 安装ESP-IDF插件 (如果还没有)
3. 按 `Ctrl+Shift+P` 打开命令面板
4. 输入 "ESP-IDF: Open Project" 并选择
5. 选择当前项目文件夹

### 2. 配置ESP-IDF
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "ESP-IDF: Configure ESP-IDF Extension"
3. 选择ESP-IDF安装路径
4. 选择Python路径
5. 选择工具链路径

### 3. 设置目标芯片
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "ESP-IDF: Set Espressif Device Target"
3. 选择 "esp32c3"

### 4. 编译项目

#### 方法A: 使用VSCode ESP-IDF插件
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "ESP-IDF: Build Project"
3. 等待编译完成

#### 方法B: 使用命令行
1. 打开"ESP-IDF Command Prompt"
2. 导航到项目目录：`cd C:\Users\<USER>\Desktop\IMU\Fusion-main`
3. 运行构建脚本：`setup_and_build.bat`

### 5. 烧录程序

#### 方法A: 使用VSCode
1. 连接ESP32-C3到电脑
2. 按 `Ctrl+Shift+P` 打开命令面板
3. 输入 "ESP-IDF: Flash Device"
4. 选择正确的串口
5. 等待烧录完成

#### 方法B: 使用命令行
```bash
# 烧录并监控
idf.py flash monitor

# 或指定串口
idf.py -p COM3 flash monitor
```

### 6. 监控输出
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "ESP-IDF: Monitor Device"
3. 查看串口输出数据

## 📊 预期输出

烧录成功后，您应该看到类似以下的输出：

```
I (1234) IMU_FUSION: ESP32-C3 IMU Fusion项目启动
I (1245) IMU_DRIVER: I2C总线初始化完成
I (1256) IMU_DRIVER: 检测到MPU6050设备 (ID: 0x68)
I (1267) IMU_DRIVER: MPU6050初始化完成
I (1278) IMU_FUSION: Fusion AHRS算法初始化完成
I (1289) IMU_FUSION: IMU数据读取任务启动
I (1300) IMU_FUSION: IMU融合任务启动
I (1311) IMU_FUSION: 所有任务创建完成，系统运行中...
I (1322) IMU_FUSION: 姿态角: Roll=1.2°, Pitch=-0.8°, Yaw=45.6°
I (1333) IMU_FUSION: 线性加速度: X=0.02g, Y=-0.01g, Z=0.00g
```

## 🔧 常见问题解决

### 问题1: 编译错误
**现象**: 编译时出现头文件找不到的错误
**解决**: 
1. 确保ESP-IDF版本 >= 5.0
2. 重新配置ESP-IDF插件
3. 清理并重新编译: `ESP-IDF: Full Clean`

### 问题2: IMU检测失败
**现象**: 串口输出显示"MPU6050设备ID错误"
**解决**:
1. 检查I2C连接线是否正确
2. 确认MPU6050供电正常 (3.3V)
3. 检查I2C地址是否为0x68

### 问题3: 数据异常
**现象**: 姿态角数据跳动很大或不合理
**解决**:
1. 确保传感器静止校准
2. 调整算法增益参数
3. 检查采样频率设置

### 问题4: 烧录失败
**现象**: 无法连接到ESP32-C3
**解决**:
1. 检查USB线连接
2. 按住BOOT按钮重新烧录
3. 确认串口驱动已安装

## 🎯 下一步

1. **校准传感器**: 根据实际应用调整传感器参数
2. **优化算法**: 调整AHRS算法参数以获得更好性能
3. **添加功能**: 集成磁力计、数据记录等功能
4. **应用开发**: 将姿态数据用于您的具体应用

## 📞 获取帮助

如果遇到问题，请：
1. 查看完整的README.md文档
2. 检查ESP-IDF官方文档
3. 在项目仓库提交Issue

祝您使用愉快！🎉
