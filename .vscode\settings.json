{"C_Cpp.intelliSenseEngine": "Tag Parser", "idf.adapterTargetName": "esp32c3", "idf.customExtraPaths": "", "idf.customExtraVars": "", "idf.espIdfPath": "", "idf.openOcdConfigs": ["board/esp32c3-builtin.cfg"], "idf.pythonBinPath": "", "idf.toolsPath": "", "files.associations": {"*.h": "c", "*.c": "c"}, "C_Cpp.default.compilerPath": "", "C_Cpp.default.cStandard": "c11", "C_Cpp.default.cppStandard": "c++17"}