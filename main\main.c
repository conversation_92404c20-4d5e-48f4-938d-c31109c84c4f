/**
 * @file main.c
 * @brief ESP32-C3 IMU Fusion主程序 - 简化测试版本
 * <AUTHOR> IMU Fusion Project
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_system.h"
#include "driver/i2c.h"

#include "imu_driver.h"

// 日志标签
static const char *TAG = "IMU_FUSION";

// 简单的测试任务
void test_task(void *pvParameters)
{
    ESP_LOGI(TAG, "ESP32-C3 IMU测试任务启动");

    // 初始化IMU
    esp_err_t ret = imu_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "IMU初始化失败: %s", esp_err_to_name(ret));
        return;
    }

    ESP_LOGI(TAG, "IMU初始化成功");

    while (1) {
        float accel_x, accel_y, accel_z;
        float gyro_x, gyro_y, gyro_z;

        // 读取IMU数据
        ret = imu_read_data(&accel_x, &accel_y, &accel_z, &gyro_x, &gyro_y, &gyro_z);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "加速度: X=%.2fg, Y=%.2fg, Z=%.2fg", accel_x, accel_y, accel_z);
            ESP_LOGI(TAG, "陀螺仪: X=%.1f°/s, Y=%.1f°/s, Z=%.1f°/s", gyro_x, gyro_y, gyro_z);
        } else {
            ESP_LOGE(TAG, "读取IMU数据失败");
        }

        vTaskDelay(pdMS_TO_TICKS(100)); // 100ms延时
    }
}

/**
 * @brief 应用程序入口点
 */
void app_main(void)
{
    ESP_LOGI(TAG, "ESP32-C3 IMU测试项目启动");

    // 创建测试任务
    xTaskCreate(test_task, "test_task", 4096, NULL, 5, NULL);

    ESP_LOGI(TAG, "测试任务创建完成");
}


