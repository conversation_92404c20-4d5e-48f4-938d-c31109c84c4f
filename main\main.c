/**
 * @file main.c
 * @brief ESP32-C3 IMU Fusion主程序
 * <AUTHOR> IMU Fusion Project
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_timer.h"
#include "driver/i2c.h"

#include "Fusion.h"
#include "imu_driver.h"

// 日志标签
static const char *TAG = "IMU_FUSION";

// IMU数据采样周期 (100Hz = 10ms)
#define SAMPLE_PERIOD_MS    10
#define SAMPLE_PERIOD_S     (SAMPLE_PERIOD_MS / 1000.0f)

// 任务优先级和堆栈大小
#define IMU_TASK_PRIORITY   5
#define IMU_TASK_STACK_SIZE 4096

// IMU数据结构
typedef struct {
    float gyro_x, gyro_y, gyro_z;      // 陀螺仪数据 (度/秒)
    float accel_x, accel_y, accel_z;   // 加速度计数据 (g)
    float mag_x, mag_y, mag_z;         // 磁力计数据 (可选)
} imu_data_t;

// 全局变量
static FusionAhrs ahrs;
static QueueHandle_t imu_data_queue;

/**
 * @brief 初始化Fusion AHRS算法
 */
static void fusion_init(void)
{
    // 初始化AHRS算法
    FusionAhrsInitialise(&ahrs);
    
    // 设置AHRS算法参数
    const FusionAhrsSettings settings = {
        .convention = FusionConventionNwu,          // 北-西-上坐标系
        .gain = 0.5f,                               // 融合增益
        .gyroscopeRange = 2000.0f,                  // 陀螺仪量程 (度/秒)
        .accelerationRejection = 10.0f,             // 加速度拒绝阈值 (度)
        .magneticRejection = 10.0f,                 // 磁场拒绝阈值 (度)
        .recoveryTriggerPeriod = 5 * (1000 / SAMPLE_PERIOD_MS), // 恢复触发周期 (5秒)
    };
    
    FusionAhrsSetSettings(&ahrs, &settings);
    
    ESP_LOGI(TAG, "Fusion AHRS算法初始化完成");
}

/**
 * @brief IMU数据处理任务
 */
static void imu_fusion_task(void *pvParameters)
{
    imu_data_t imu_data;
    TickType_t last_wake_time = xTaskGetTickCount();
    
    ESP_LOGI(TAG, "IMU融合任务启动");
    
    while (1) {
        // 等待IMU数据
        if (xQueueReceive(imu_data_queue, &imu_data, portMAX_DELAY) == pdTRUE) {
            
            // 准备Fusion库所需的数据格式
            const FusionVector gyroscope = {
                .axis.x = imu_data.gyro_x,
                .axis.y = imu_data.gyro_y,
                .axis.z = imu_data.gyro_z
            };
            
            const FusionVector accelerometer = {
                .axis.x = imu_data.accel_x,
                .axis.y = imu_data.accel_y,
                .axis.z = imu_data.accel_z
            };
            
            // 如果有磁力计数据，使用完整的AHRS更新
            if (imu_data.mag_x != 0 || imu_data.mag_y != 0 || imu_data.mag_z != 0) {
                const FusionVector magnetometer = {
                    .axis.x = imu_data.mag_x,
                    .axis.y = imu_data.mag_y,
                    .axis.z = imu_data.mag_z
                };
                FusionAhrsUpdate(&ahrs, gyroscope, accelerometer, magnetometer, SAMPLE_PERIOD_S);
            } else {
                // 仅使用陀螺仪和加速度计
                FusionAhrsUpdateNoMagnetometer(&ahrs, gyroscope, accelerometer, SAMPLE_PERIOD_S);
            }
            
            // 获取姿态角度
            const FusionEuler euler = FusionQuaternionToEuler(FusionAhrsGetQuaternion(&ahrs));
            
            // 获取线性加速度（去除重力）
            const FusionVector linearAcceleration = FusionAhrsGetLinearAcceleration(&ahrs);
            
            // 打印结果
            ESP_LOGI(TAG, "姿态角: Roll=%.1f°, Pitch=%.1f°, Yaw=%.1f°", 
                     euler.angle.roll, euler.angle.pitch, euler.angle.yaw);
            ESP_LOGI(TAG, "线性加速度: X=%.2fg, Y=%.2fg, Z=%.2fg",
                     linearAcceleration.axis.x, linearAcceleration.axis.y, linearAcceleration.axis.z);
        }
        
        // 控制任务执行频率
        vTaskDelayUntil(&last_wake_time, pdMS_TO_TICKS(SAMPLE_PERIOD_MS));
    }
}

/**
 * @brief IMU数据读取任务
 */
static void imu_read_task(void *pvParameters)
{
    imu_data_t imu_data;
    TickType_t last_wake_time = xTaskGetTickCount();
    
    ESP_LOGI(TAG, "IMU数据读取任务启动");
    
    while (1) {
        // 读取IMU数据
        if (imu_read_data(&imu_data.accel_x, &imu_data.accel_y, &imu_data.accel_z,
                         &imu_data.gyro_x, &imu_data.gyro_y, &imu_data.gyro_z) == ESP_OK) {
            
            // 尝试读取磁力计数据（如果支持）
            imu_data.mag_x = 0;
            imu_data.mag_y = 0; 
            imu_data.mag_z = 0;
            
            // 发送数据到处理队列
            if (xQueueSend(imu_data_queue, &imu_data, 0) != pdTRUE) {
                ESP_LOGW(TAG, "IMU数据队列满，丢弃数据");
            }
        } else {
            ESP_LOGE(TAG, "读取IMU数据失败");
        }
        
        // 控制采样频率
        vTaskDelayUntil(&last_wake_time, pdMS_TO_TICKS(SAMPLE_PERIOD_MS));
    }
}

/**
 * @brief 应用程序入口点
 */
void app_main(void)
{
    ESP_LOGI(TAG, "ESP32-C3 IMU Fusion项目启动");
    
    // 初始化IMU传感器
    ESP_ERROR_CHECK(imu_init());
    ESP_LOGI(TAG, "IMU传感器初始化完成");
    
    // 初始化Fusion算法
    fusion_init();
    
    // 创建IMU数据队列
    imu_data_queue = xQueueCreate(10, sizeof(imu_data_t));
    if (imu_data_queue == NULL) {
        ESP_LOGE(TAG, "创建IMU数据队列失败");
        return;
    }
    
    // 创建IMU数据读取任务
    xTaskCreate(imu_read_task, "imu_read", IMU_TASK_STACK_SIZE, NULL, IMU_TASK_PRIORITY, NULL);
    
    // 创建IMU数据融合处理任务
    xTaskCreate(imu_fusion_task, "imu_fusion", IMU_TASK_STACK_SIZE, NULL, IMU_TASK_PRIORITY + 1, NULL);
    
    ESP_LOGI(TAG, "所有任务创建完成，系统运行中...");
}
