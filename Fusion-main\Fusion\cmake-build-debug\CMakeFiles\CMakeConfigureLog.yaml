
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:233 (message)"
      - "CMakeLists.txt"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        C:/Users/<USER>/Desktop/Fusion-main/Fusion-main/Fusion/cmake-build-debug/CMakeFiles/3.28.6/CompilerIdC/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        C:/Users/<USER>/Desktop/Fusion-main/Fusion-main/Fusion/cmake-build-debug/CMakeFiles/3.28.6/CompilerIdCXX/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:60 (try_compile)"
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/Fusion-main/Fusion-main/Fusion/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-w564vy"
      binary: "C:/Users/<USER>/Desktop/Fusion-main/Fusion-main/Fusion/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-w564vy"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Fusion-main/Fusion-main/Fusion/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-w564vy'
        
        Run Build Command(s): "D:/CLion 2024.3/CLion 2024.1.6/bin/ninja/win/x64/ninja.exe" -v cmTC_05723
        [1/2] "D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\gcc.exe"   -fdiagnostics-color=always   -v -o CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj -c "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCCompilerABI.c"
        Using built-in specs.
        COLLECT_GCC=D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\gcc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 13.1.0 (GCC) 
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_05723.dir/'
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/cc1.exe -quiet -v -iprefix D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/ -D_REENTRANT D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_05723.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -fdiagnostics-color=always -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKAfEGe.s
        GNU C17 (GCC) version 13.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 13.1.0, GMP version 6.2.1, MPFR version 4.2.0-p4, MPC version 1.3.1, isl version none
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include"
        ignoring nonexistent directory "/win/include"
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../include"
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed"
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: 2aa4fcf5c9208168c5e2d38a58fc2a97
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_05723.dir/'
         as -v -o CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKAfEGe.s
        GNU assembler version 2.40 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.40
        COMPILER_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/
        LIBRARY_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../\x0d
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.'\x0d
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\gcc.exe"  -v CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj -o cmTC_05723.exe -Wl,--out-implib,libcmTC_05723.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        Using built-in specs.
        COLLECT_GCC=D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=D:/CLion\\ 2024.3/CLion\\ 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 13.1.0 (GCC) 
        COMPILER_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/
        LIBRARY_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_05723.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_05723.'
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/collect2.exe -m i386pep -Bdynamic -o cmTC_05723.exe D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_05723.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_05723.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_05723.'\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:130 (message)"
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include]
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include]
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include]
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/include]
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/include;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:162 (message)"
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld\\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: 'C:/Users/<USER>/Desktop/Fusion-main/Fusion-main/Fusion/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-w564vy']
        ignore line: []
        ignore line: [Run Build Command(s): "D:/CLion 2024.3/CLion 2024.1.6/bin/ninja/win/x64/ninja.exe" -v cmTC_05723]
        ignore line: [[1/2] "D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\gcc.exe"   -fdiagnostics-color=always   -v -o CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj -c "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCCompilerABI.c"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\gcc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.1.0 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_05723.dir/']
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/cc1.exe -quiet -v -iprefix D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/ -D_REENTRANT D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_05723.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -fdiagnostics-color=always -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKAfEGe.s]
        ignore line: [GNU C17 (GCC) version 13.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 13.1.0  GMP version 6.2.1  MPFR version 4.2.0-p4  MPC version 1.3.1  isl version none]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include"]
        ignore line: [ignoring nonexistent directory "/win/include"]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../include"]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 2aa4fcf5c9208168c5e2d38a58fc2a97]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_05723.dir/']
        ignore line: [ as -v -o CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKAfEGe.s]
        ignore line: [GNU assembler version 2.40 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.40]
        ignore line: [COMPILER_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../\x0d]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.'\x0d]
        ignore line: [[2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\gcc.exe"  -v CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj -o cmTC_05723.exe -Wl --out-implib libcmTC_05723.dll.a -Wl --major-image-version 0 --minor-image-version 0   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/CLion\\ 2024.3/CLion\\ 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.1.0 (GCC) ]
        ignore line: [COMPILER_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_05723.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_05723.']
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/collect2.exe -m i386pep -Bdynamic -o cmTC_05723.exe D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. CMakeFiles/cmTC_05723.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_05723.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_05723.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_05723.'\x0d]
        ignore line: []
        ignore line: []
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:60 (try_compile)"
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/Fusion-main/Fusion-main/Fusion/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-kwe65t"
      binary: "C:/Users/<USER>/Desktop/Fusion-main/Fusion-main/Fusion/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-kwe65t"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Fusion-main/Fusion-main/Fusion/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-kwe65t'
        
        Run Build Command(s): "D:/CLion 2024.3/CLion 2024.1.6/bin/ninja/win/x64/ninja.exe" -v cmTC_45696
        [1/2] "D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\g++.exe"   -fdiagnostics-color=always   -v -o CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj -c "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp"
        Using built-in specs.
        COLLECT_GCC=D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\g++.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 13.1.0 (GCC) 
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_45696.dir/'
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/cc1plus.exe -quiet -v -iprefix D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/ -D_REENTRANT D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_45696.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=nocona -version -fdiagnostics-color=always -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccR5IXRD.s
        GNU C++17 (GCC) version 13.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 13.1.0, GMP version 6.2.1, MPFR version 4.2.0-p4, MPC version 1.3.1, isl version none
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++"
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32"
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward"
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include"
        ignoring nonexistent directory "/win/include"
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../include"
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed"
        ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: e75de627edc3c57e31324b930b15b056
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_45696.dir/'
         as -v -o CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccR5IXRD.s
        GNU assembler version 2.40 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.40
        COMPILER_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/
        LIBRARY_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../\x0d
        COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.'\x0d
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\g++.exe"  -v CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_45696.exe -Wl,--out-implib,libcmTC_45696.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        Using built-in specs.
        COLLECT_GCC=D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\g++.exe
        COLLECT_LTO_WRAPPER=D:/CLion\\ 2024.3/CLion\\ 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends
        Thread model: posix
        Supported LTO compression algorithms: zlib
        gcc version 13.1.0 (GCC) 
        COMPILER_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/
        LIBRARY_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_45696.exe' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_45696.'
         D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/collect2.exe -m i386pep -Bdynamic -o cmTC_45696.exe D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_45696.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o\x0d
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_45696.exe' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_45696.'\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:130 (message)"
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++]
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32]
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward]
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include]
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include]
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
          add: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++]
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32]
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward]
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include]
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/include]
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
        collapse include dir [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include] ==> [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/include;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed;D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:162 (message)"
      - "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld\\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: 'C:/Users/<USER>/Desktop/Fusion-main/Fusion-main/Fusion/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-kwe65t']
        ignore line: []
        ignore line: [Run Build Command(s): "D:/CLion 2024.3/CLion 2024.1.6/bin/ninja/win/x64/ninja.exe" -v cmTC_45696]
        ignore line: [[1/2] "D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\g++.exe"   -fdiagnostics-color=always   -v -o CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj -c "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\g++.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.1.0 (GCC) ]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_45696.dir/']
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/cc1plus.exe -quiet -v -iprefix D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/ -D_REENTRANT D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_45696.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=nocona -version -fdiagnostics-color=always -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccR5IXRD.s]
        ignore line: [GNU C++17 (GCC) version 13.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 13.1.0  GMP version 6.2.1  MPFR version 4.2.0-p4  MPC version 1.3.1  isl version none]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++"]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward"]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include"]
        ignore line: [ignoring nonexistent directory "/win/include"]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../include"]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../include]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed]
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: e75de627edc3c57e31324b930b15b056]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_45696.dir/']
        ignore line: [ as -v -o CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccR5IXRD.s]
        ignore line: [GNU assembler version 2.40 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.40]
        ignore line: [COMPILER_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../\x0d]
        ignore line: [COLLECT_GCC_OPTIONS='-fdiagnostics-color=always' '-v' '-o' 'CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.'\x0d]
        ignore line: [[2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\g++.exe"  -v CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_45696.exe -Wl --out-implib libcmTC_45696.dll.a -Wl --major-image-version 0 --minor-image-version 0   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\CLion 2024.3\\CLion 2024.1.6\\bin\\mingw\\bin\\g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/CLion\\ 2024.3/CLion\\ 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-13.1.0/configure --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --build=x86_64-alpine-linux-musl --prefix=/win --enable-checking=release --enable-fully-dynamic-string --enable-languages=c,c++ --with-arch=nocona --with-tune=generic --enable-libatomic --enable-libgomp --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --enable-seh-exceptions --enable-shared --enable-static --enable-threads=posix --enable-version-specific-runtime-libs --disable-bootstrap --disable-graphite --disable-libada --disable-libstdcxx-pch --disable-libstdcxx-debug --disable-libquadmath --disable-lto --disable-nls --disable-multilib --disable-rpath --disable-symvers --disable-werror --disable-win32-registry --with-gnu-as --with-gnu-ld --with-system-libiconv --with-system-libz --with-gmp=/win/makedepends --with-mpfr=/win/makedepends --with-mpc=/win/makedepends]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.1.0 (GCC) ]
        ignore line: [COMPILER_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/]
        ignore line: [LIBRARY_PATH=D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_45696.exe' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_45696.']
        ignore line: [ D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../libexec/gcc/x86_64-w64-mingw32/13.1.0/collect2.exe -m i386pep -Bdynamic -o cmTC_45696.exe D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtbegin.o -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0 -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib -LD:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../.. CMakeFiles/cmTC_45696.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_45696.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/../lib/gcc/x86_64-w64-mingw32/13.1.0/crtend.o\x0d]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_45696.exe' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_45696.'\x0d]
        ignore line: []
        ignore line: []
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
...
