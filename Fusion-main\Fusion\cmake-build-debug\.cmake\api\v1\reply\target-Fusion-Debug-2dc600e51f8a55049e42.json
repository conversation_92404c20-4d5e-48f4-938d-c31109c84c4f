{"archive": {}, "artifacts": [{"path": "libFusion.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -fdiagnostics-color=always"}], "language": "C", "sourceIndexes": [0, 1, 2, 3]}], "id": "Fusion::@6890427a1f51a3e7e1df", "name": "Fusion", "nameOnDisk": "libFusion.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "FusionAhrs.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "FusionCompass.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "FusionOffset.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "cmake-build-debug/CMakeFiles/3.28.6/CompilerIdC/CMakeCCompilerId.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}